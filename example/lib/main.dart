import 'package:chat_flutter_example/ui/design_spec.dart';
import 'package:flutter/material.dart';
import 'pages/chat_session_list_page.dart';
import 'pages/storyboard_page.dart';
import 'pages/bulge_clip_demo_page.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with SingleTickerProviderStateMixin {
  TabController? tabController;

  @override
  void initState() {
    super.initState();

    tabController = TabController(length: 3, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      color: DesignSpec.primaryBackground,
      home: DefaultTabController(
        length: 3,
        child: Scaffold(
          backgroundColor: DesignSpec.primaryBackground,
          body: Stack(
            children: [
              Positioned.fill(
                child: Tab<PERSON>ar<PERSON>iew(
                  controller: tabController,
                  children: [
                    StoryBoardPage(),
                    ChatSessionListPage(),
                    _buildProfilePage(),
                  ],
                ),
              ),
              Positioned(
                  bottom: MediaQuery.of(context).padding.bottom + 15,
                  left: 20,
                  right: 20,
                  child: Container(
                    height: 70,
                    decoration: BoxDecoration(
                      color: DesignSpec.secondaryBackground,
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: Offset(0, 5),
                        ),
                      ],
                    ),
                  )
              ),
              Positioned(
                bottom: 20,
                left: 20,
                right: 20,
                child: BottomNavigationBar(
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  currentIndex: tabController?.index ?? 0,
                  onTap: (index) {
                    tabController?.animateTo(index);
                    setState(() {});
                  },
                  selectedItemColor: DesignSpec.primaryItemSelected,
                  unselectedItemColor: DesignSpec.primaryItemUnselected,
                  items: const [
                    BottomNavigationBarItem(
                        icon: Icon(Icons.menu_book), label: 'StoryBoard'),
                    BottomNavigationBarItem(
                        icon: Icon(Icons.chat), label: 'Chat'),
                    BottomNavigationBarItem(
                        icon: Icon(Icons.person), label: 'Profile'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfilePage() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            'Profile',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const BulgeClipDemoPage(),
                ),
              );
            },
            icon: const Icon(Icons.crop),
            label: const Text('枕头形状裁剪演示'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }
}
