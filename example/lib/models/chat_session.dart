import 'message.dart';

class ChatSession {
  final String id;
  final String title;
  final String? avatarUrl;
  final List<Message> messages;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final int unreadCount;

  ChatSession({
    required this.id,
    required this.title,
    this.avatarUrl,
    required this.messages,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.unreadCount = 0,
  });

  // 获取最后一条消息
  Message? get lastMessage {
    if (messages.isEmpty) return null;
    return messages.last;
  }

  // 获取最后一条消息的预览文本
  String get lastMessagePreview {
    final message = lastMessage;
    if (message == null) return '暂无消息';

    switch (message.type) {
      case MessageType.system:
        return message.content;
      case MessageType.image:
        return '[图片]${message.content.isNotEmpty ? ' ${message.content}' : ''}';
      case MessageType.text:
        return message.content;
    }
  }

  // 获取格式化的时间显示
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(updatedAt);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${updatedAt.month}/${updatedAt.day}';
    }
  }

  ChatSession copyWith({
    String? id,
    String? title,
    String? avatarUrl,
    List<Message>? messages,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    int? unreadCount,
  }) {
    return ChatSession(
      id: id ?? this.id,
      title: title ?? this.title,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      messages: messages ?? this.messages,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }

  // 添加消息
  ChatSession addMessage(Message message) {
    final newMessages = List<Message>.from(messages)..add(message);
    return copyWith(
      messages: newMessages,
      updatedAt: message.timestamp,
      unreadCount: message.sender != MessageSender.user ? unreadCount + 1 : unreadCount,
    );
  }

  // 标记所有消息为已读
  ChatSession markAllAsRead() {
    final readMessages = messages.map((msg) => msg.copyWith(isRead: true)).toList();
    return copyWith(
      messages: readMessages,
      unreadCount: 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'avatarUrl': avatarUrl,
      'messages': messages.map((msg) => msg.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isActive': isActive,
      'unreadCount': unreadCount,
    };
  }

  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      id: json['id'] as String,
      title: json['title'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      messages: (json['messages'] as List<dynamic>)
          .map((msgJson) => Message.fromJson(msgJson as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isActive: json['isActive'] as bool? ?? true,
      unreadCount: json['unreadCount'] as int? ?? 0,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatSession && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ChatSession(id: $id, title: $title, messagesCount: ${messages.length}, updatedAt: $updatedAt)';
  }
}