import 'package:flutter/material.dart';
import 'package:chat_flutter/widgets/bulge_clip_widget.dart';

class BulgeClipDemoPage extends StatefulWidget {
  const BulgeClipDemoPage({Key? key}) : super(key: key);

  @override
  State<BulgeClipDemoPage> createState() => _BulgeClipDemoPageState();
}

class _BulgeClipDemoPageState extends State<BulgeClipDemoPage> {
  double _bulgeHeight = BulgePresets.moderate;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('枕头形状裁剪效果演示'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 控制面板
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '凸出程度控制',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    Slider(
                      value: _bulgeHeight,
                      min: 0,
                      max: 50,
                      divisions: 50,
                      label: _bulgeHeight.round().toString(),
                      onChanged: (value) {
                        setState(() {
                          _bulgeHeight = value;
                        });
                      },
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        _buildPresetButton('微妙', BulgePresets.subtle),
                        _buildPresetButton('适中', BulgePresets.moderate),
                        _buildPresetButton('明显', BulgePresets.pronounced),
                        _buildPresetButton('夸张', BulgePresets.dramatic),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 演示区域
            Text(
              '效果演示',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            // 图片演示
            _buildDemoSection(
              title: '渐变背景裁剪效果',
              child: Container(
                height: 200,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.blue.shade800,
                      Colors.blue.shade400,
                      Colors.cyan.shade300,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.nights_stay,
                        size: 60,
                        color: Colors.white,
                      ),
                      SizedBox(height: 8),
                      Text(
                        '夜空场景',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 文本内容演示
            _buildDemoSection(
              title: '文本卡片裁剪效果',
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.purple.shade400, Colors.pink.shade400],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                child: const Column(
                  children: [
                    Text(
                      '枕头形状裁剪',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      '上下边缘向外凸出\n左右边缘向内凹陷\n形成独特的视觉效果',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 图标演示
            _buildDemoSection(
              title: '图标卡片裁剪效果',
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade400, Colors.red.shade400],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Icon(Icons.star, size: 40, color: Colors.white),
                    Icon(Icons.favorite, size: 40, color: Colors.white),
                    Icon(Icons.thumb_up, size: 40, color: Colors.white),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPresetButton(String label, double value) {
    final isSelected = (_bulgeHeight - value).abs() < 0.1;
    return ElevatedButton(
      onPressed: () {
        setState(() {
          _bulgeHeight = value;
        });
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? Colors.blue : null,
        foregroundColor: isSelected ? Colors.white : null,
      ),
      child: Text(label),
    );
  }

  Widget _buildDemoSection({
    required String title,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            // 原始效果
            Expanded(
              child: Column(
                children: [
                  const Text('原始效果', style: TextStyle(fontSize: 12)),
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: child,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            // 裁剪效果
            Expanded(
              child: Column(
                children: [
                  const Text('裁剪效果', style: TextStyle(fontSize: 12)),
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: BulgeClipWidget(
                      bulgeHeight: _bulgeHeight,
                      child: child,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
