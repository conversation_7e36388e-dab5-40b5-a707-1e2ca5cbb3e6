import 'package:flutter/material.dart';
import '../ui/design_spec.dart';
import '../models/story_category.dart';
import '../models/story.dart';
import '../utils/screen_util.dart';
import 'package:chat_flutter/widgets/bulge_clip_widget.dart';
import '../widgets/story_category_chip.dart';

class StoryBoardPage extends StatefulWidget {
  const StoryBoardPage({super.key});

  @override
  State<StoryBoardPage> createState() => _StoryBoardPageState();
}

class _StoryBoardPageState extends State<StoryBoardPage> {
  final TextEditingController _searchCtrl = TextEditingController();
  bool _loading = false;
  String? _error;

  late List<StoryCategory> _categories;
  String _selectedCategoryId = '';
  late List<Story> _allStories;

  @override
  void initState() {
    super.initState();
    _initData();
  }

  void _initData() async {
    setState(() {
      _loading = true;
      _error = null;
    });

    try {
      // 模拟从网络/本地加载数据。可以替换为实际API。
      _categories = const [
        StoryCategory(id: 'prophets', name: 'Prophet<PERSON>', icon: Icons.eco, color: Color(0xFF9CD77B)),
        StoryCategory(id: 'sahaba', name: 'Sahaba', icon: Icons.park, color: Color(0xFF9FE3C4)),
        StoryCategory(id: 'morals', name: 'Morals', icon: Icons.favorite, color: Color(0xFFF5A08E)),
        StoryCategory(id: 'heros', name: "Hero's", icon: Icons.person, color: Color(0xFFC4B8F0)),
      ];
      _selectedCategoryId = _categories.first.id;

      _allStories = [
        const Story(
          id: '1',
          title: 'The Story of Hijrah',
          categoryId: 'history',
          imageUrl: 'https://picsum.photos/seed/hijrah/400/300',
          popularity: 10,
        ),
        const Story(
          id: '2',
          title: 'The Battle of Badr',
          categoryId: 'history',
          imageUrl: 'https://picsum.photos/seed/badr/400/300',
          popularity: 9,
        ),
        const Story(
          id: '3',
          title: 'Early Days',
          categoryId: 'history',
          imageUrl: 'https://picsum.photos/seed/early/400/300',
          popularity: 7,
        ),
        const Story(
          id: '4',
          title: 'Story of Prophet Musa',
          categoryId: 'prophets',
          imageUrl: 'https://picsum.photos/seed/musa/400/300',
          popularity: 12,
        ),
      ];
    } catch (e) {
      _error = '加载失败，请稍后重试';
    } finally {
      setState(() {
        _loading = false;
      });
    }
  }

  List<Story> get _filteredStories {
    final q = _searchCtrl.text.trim().toLowerCase();
    final byCat = _allStories.where((s) =>
        _selectedCategoryId.isEmpty || s.categoryId == _selectedCategoryId);
    if (q.isEmpty) return byCat.toList();
    return byCat.where((s) => s.title.toLowerCase().contains(q)).toList();
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_error != null) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(_error!),
            const SizedBox(height: 8),
            ElevatedButton(onPressed: _initData, child: const Text('重试')),
          ],
        ),
      );
    }

    return SafeArea(
      child: RefreshIndicator(
        onRefresh: () async => _initData(),
        color: DesignSpec.primaryItemSelected,
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
          slivers: [
            SliverToBoxAdapter(child: _buildTopBar()),
            SliverToBoxAdapter(child: _buildSearchBar()),
            SliverToBoxAdapter(child: _buildCategories()),
            SliverToBoxAdapter(child: _buildSectionTitle("Category Stories")),
            _buildStoryGrid(_filteredStories),
            SliverToBoxAdapter(child: _buildSectionTitle('Most Popular')),
            _buildPopularList(),
            const SliverToBoxAdapter(child: SizedBox(height: 100)),
          ],
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
      child: Row(
        children: [
          const Expanded(
            child: Text(
              'DreamTales',
              style: TextStyle(
                fontSize: DesignSpec.fontSizeXl,
                fontWeight: DesignSpec.fontWeightBold,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.amber[200],
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 6,
                  offset: const Offset(0, 3),
                )
              ],
            ),
            child: Row(
              children: const [
                Icon(Icons.star, color: Color(0xFFEE864E), size: 18),
                SizedBox(width: 4),
                Text('00', style: TextStyle(fontWeight: DesignSpec.fontWeightBold)),
              ],
            ),
          ),
          const SizedBox(width: 12),
          const CircleAvatar(radius: 18, backgroundColor: Colors.grey, child: Icon(Icons.person, color: Colors.white)),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            )
          ],
        ),
        child: Row(
          children: [
            const SizedBox(width: 16),
            const Icon(Icons.search, color: Colors.black54),
            const SizedBox(width: 8),
            Expanded(
              child: TextField(
                controller: _searchCtrl,
                onChanged: (_) => setState(() {}),
                decoration: const InputDecoration(
                  hintText: 'Search for a story',
                  border: InputBorder.none,
                ),
              ),
            ),
            IconButton(
              onPressed: () {},
              icon: const Icon(Icons.mic, color: Color(0xFFFF7F4D)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategories() {
    return Padding(
      padding: const EdgeInsets.only(left: 24, right: 24, top: 8, bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Story Categories',
            style: TextStyle(fontSize: DesignSpec.fontSizeBase, fontWeight: DesignSpec.fontWeightBold),
          ),
          const SizedBox(height: 12),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            physics: const BouncingScrollPhysics(),
            child: Row(
              children: [
                for (final c in _categories) ...[
                  StoryCategoryChip(
                    icon: c.icon,
                    label: c.name,
                    color: c.color,
                    selected: c.id == _selectedCategoryId,
                    onTap: () => setState(() => _selectedCategoryId = c.id),
                  ),
                  const SizedBox(width: 16),
                ]
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 4, 24, 8),
      child: Text(
        title,
        style: const TextStyle(fontSize: DesignSpec.fontSizeBase, fontWeight: DesignSpec.fontWeightBold),
      ),
    );
  }

  SliverGrid _buildStoryGrid(List<Story> stories) {
    return SliverGrid(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final s = stories[index];
          return _StoryCard(story: s);
        },
        childCount: stories.length,
      ),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 14,
        crossAxisSpacing: 14,
        childAspectRatio: 0.86,
      ),
    );
  }

  SliverList _buildPopularList() {
    final popular = _allStories.toList()
      ..sort((a, b) => b.popularity.compareTo(a.popularity));
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
            child: _PopularTile(story: popular[index]),
          );
        },
        childCount: popular.length,
      ),
    );
  }
}

class _StoryCard extends StatelessWidget {
  final Story story;
  const _StoryCard({required this.story});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: BulgeClipWidget(
            bulgeHeight: 20.0, // 网格卡片使用较小的凸出效果
            child: Image.network(
              story.imageUrl,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.blue.shade300, Colors.purple.shade300],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.auto_stories,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          story.title,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(fontWeight: DesignSpec.fontWeightBold),
        ),
      ],
    );
  }
}

class _PopularTile extends StatelessWidget {
  final Story story;
  const _PopularTile({required this.story});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 使用BulgeClipWidget实现枕头形状的图片裁剪效果
          BulgeClipWidget(
            bulgeHeight: 25.0, // 适中的凸出效果
            child: Image.network(
              story.imageUrl,
              width: double.infinity,
              height: 120,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: double.infinity,
                  height: 120,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.grey.shade300, Colors.grey.shade400],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Text(
                  story.title,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontWeight: DesignSpec.fontWeightBold,
                    fontSize: DesignSpec.fontSizeBase,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.star,
                      color: Colors.amber.shade600,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${story.popularity}',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

