import 'package:flutter/material.dart';

/// 自定义裁剪器，通过裁剪四个角来实现上下边缘向外凸出的视觉效果
class BulgeClipper extends CustomClipper<Path> {
  final double bulgeHeight;
  
  const BulgeClipper({
    this.bulgeHeight = 20.0,
  });

  @override
  Path getClip(Size size) {
    final path = Path();
    final width = size.width;
    final height = size.height;
    
    // 计算控制点，用于创建弧形效果
    final controlPointOffset = bulgeHeight;
    
    // 从左上角开始，但不是从真正的角落开始
    path.moveTo(controlPointOffset, 0);
    
    // 上边缘：创建向外凸出的弧形
    // 使用二次贝塞尔曲线，控制点在上方
    path.quadraticBezierTo(
      width / 2, -controlPointOffset, // 控制点在上方
      width - controlPointOffset, 0, // 结束点
    );
    
    // 右边缘
    path.lineTo(width, controlPointOffset);
    path.lineTo(width, height - controlPointOffset);
    
    // 下边缘：创建向外凸出的弧形
    // 使用二次贝塞尔曲线，控制点在下方
    path.quadraticBezierTo(
      width / 2, height + controlPointOffset, // 控制点在下方
      controlPointOffset, height, // 结束点
    );
    
    // 左边缘
    path.lineTo(0, height - controlPointOffset);
    path.lineTo(0, controlPointOffset);
    
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return oldClipper is BulgeClipper && oldClipper.bulgeHeight != bulgeHeight;
  }
}

/// 自定义Widget，通过裁剪实现上下边缘向外凸出的视觉效果
class BulgeClipWidget extends StatelessWidget {
  /// 要裁剪的子Widget
  final Widget child;
  
  /// 凸出效果的高度，值越大凸出效果越明显
  final double bulgeHeight;
  
  /// 裁剪行为，默认为antiAlias以获得平滑边缘
  final Clip clipBehavior;

  const BulgeClipWidget({
    Key? key,
    required this.child,
    this.bulgeHeight = 20.0,
    this.clipBehavior = Clip.antiAlias,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: BulgeClipper(bulgeHeight: bulgeHeight),
      clipBehavior: clipBehavior,
      child: child,
    );
  }
}

/// 预设的凸出效果程度
class BulgePresets {
  static const double subtle = 10.0;
  static const double moderate = 20.0;
  static const double pronounced = 30.0;
  static const double dramatic = 40.0;
}
