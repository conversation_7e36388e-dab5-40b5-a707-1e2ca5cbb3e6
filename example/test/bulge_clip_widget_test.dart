import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:chat_flutter/widgets/bulge_clip_widget.dart';

void main() {
  group('BulgeClipWidget Tests', () {
    testWidgets('BulgeClipWidget should render without errors', (WidgetTester tester) async {
      // 创建一个简单的测试Widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BulgeClipWidget(
              bulgeHeight: 20.0,
              child: Container(
                width: 200,
                height: 100,
                color: Colors.blue,
                child: const Center(
                  child: Text(
                    'Test Content',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ),
          ),
        ),
      );

      // 验证Widget是否正确渲染
      expect(find.byType(BulgeClipWidget), findsOneWidget);
      expect(find.byType(ClipPath), findsOneWidget);
      expect(find.text('Test Content'), findsOneWidget);
    });

    testWidgets('BulgeClipWidget should accept different bulge heights', (WidgetTester tester) async {
      const testHeights = [10.0, 20.0, 30.0, 40.0];
      
      for (final height in testHeights) {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: BulgeClipWidget(
                bulgeHeight: height,
                child: Container(
                  width: 200,
                  height: 100,
                  color: Colors.red,
                ),
              ),
            ),
          ),
        );

        // 验证Widget渲染正常
        expect(find.byType(BulgeClipWidget), findsOneWidget);
        
        // 获取BulgeClipWidget实例并验证bulgeHeight属性
        final bulgeClipWidget = tester.widget<BulgeClipWidget>(find.byType(BulgeClipWidget));
        expect(bulgeClipWidget.bulgeHeight, equals(height));
      }
    });

    testWidgets('BulgeClipWidget should use preset values correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BulgeClipWidget(
              bulgeHeight: BulgePresets.moderate,
              child: Container(
                width: 200,
                height: 100,
                color: Colors.green,
              ),
            ),
          ),
        ),
      );

      final bulgeClipWidget = tester.widget<BulgeClipWidget>(find.byType(BulgeClipWidget));
      expect(bulgeClipWidget.bulgeHeight, equals(BulgePresets.moderate));
      expect(bulgeClipWidget.bulgeHeight, equals(20.0));
    });
  });

  group('BulgeClipper Tests', () {
    test('BulgeClipper should create valid path', () {
      const clipper = BulgeClipper(bulgeHeight: 20.0);
      const size = Size(200, 100);
      
      final path = clipper.getClip(size);
      
      // 验证路径不为空
      expect(path, isNotNull);
      
      // 验证路径的边界框应该在合理范围内
      final bounds = path.getBounds();
      expect(bounds.width, lessThanOrEqualTo(size.width));
      expect(bounds.height, lessThanOrEqualTo(size.height + 40)); // 考虑凸出效果
    });

    test('BulgeClipper should reclip when bulgeHeight changes', () {
      const clipper1 = BulgeClipper(bulgeHeight: 20.0);
      const clipper2 = BulgeClipper(bulgeHeight: 30.0);
      const clipper3 = BulgeClipper(bulgeHeight: 20.0);
      
      // 不同的bulgeHeight应该需要重新裁剪
      expect(clipper1.shouldReclip(clipper2), isTrue);
      
      // 相同的bulgeHeight不需要重新裁剪
      expect(clipper1.shouldReclip(clipper3), isFalse);
    });
  });

  group('BulgePresets Tests', () {
    test('BulgePresets should have correct values', () {
      expect(BulgePresets.subtle, equals(10.0));
      expect(BulgePresets.moderate, equals(20.0));
      expect(BulgePresets.pronounced, equals(30.0));
      expect(BulgePresets.dramatic, equals(40.0));
    });
  });
}
