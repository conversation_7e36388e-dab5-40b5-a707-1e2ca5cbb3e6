import 'package:flutter_test/flutter_test.dart';
import 'package:chat_flutter_example/models/message.dart';
import 'package:chat_flutter_example/models/chat_session.dart';

void main() {
  group('Message Model Tests', () {
    test('should create system message correctly', () {
      final message = Message.systemMessage(
        id: 'test1',
        content: 'Welcome to the chat!',
      );

      expect(message.id, 'test1');
      expect(message.type, MessageType.system);
      expect(message.sender, MessageSender.system);
      expect(message.content, 'Welcome to the chat!');
      expect(message.isRead, true);
    });

    test('should create text message correctly', () {
      final message = Message.textMessage(
        id: 'test2',
        content: 'Hello world!',
        sender: MessageSender.user,
      );

      expect(message.id, 'test2');
      expect(message.type, MessageType.text);
      expect(message.sender, MessageSender.user);
      expect(message.content, 'Hello world!');
      expect(message.isRead, false);
    });

    test('should create image message correctly', () {
      final message = Message.imageMessage(
        id: 'test3',
        imageUrl: 'https://example.com/image.jpg',
        content: 'A beautiful image',
      );

      expect(message.id, 'test3');
      expect(message.type, MessageType.image);
      expect(message.sender, MessageSender.assistant);
      expect(message.imageUrl, 'https://example.com/image.jpg');
      expect(message.content, 'A beautiful image');
    });

    test('should serialize and deserialize message correctly', () {
      final originalMessage = Message.textMessage(
        id: 'test4',
        content: 'Test serialization',
        sender: MessageSender.user,
      );

      final json = originalMessage.toJson();
      final deserializedMessage = Message.fromJson(json);

      expect(deserializedMessage.id, originalMessage.id);
      expect(deserializedMessage.type, originalMessage.type);
      expect(deserializedMessage.sender, originalMessage.sender);
      expect(deserializedMessage.content, originalMessage.content);
    });
  });

  group('ChatSession Model Tests', () {
    test('should create chat session correctly', () {
      final now = DateTime.now();
      final messages = [
        Message.systemMessage(id: '1', content: 'Welcome!'),
        Message.textMessage(id: '2', content: 'Hello', sender: MessageSender.user),
      ];

      final session = ChatSession(
        id: 'session1',
        title: 'Test Chat',
        messages: messages,
        createdAt: now,
        updatedAt: now,
      );

      expect(session.id, 'session1');
      expect(session.title, 'Test Chat');
      expect(session.messages.length, 2);
      expect(session.lastMessage?.content, 'Hello');
    });

    test('should get correct last message preview', () {
      final session = ChatSession(
        id: 'session2',
        title: 'Test Chat',
        messages: [
          Message.imageMessage(id: '1', imageUrl: 'test.jpg', content: 'Image description'),
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(session.lastMessagePreview, '[图片] Image description');
    });

    test('should add message correctly', () {
      final session = ChatSession(
        id: 'session3',
        title: 'Test Chat',
        messages: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final newMessage = Message.textMessage(
        id: '1',
        content: 'New message',
        sender: MessageSender.user,
      );

      final updatedSession = session.addMessage(newMessage);

      expect(updatedSession.messages.length, 1);
      expect(updatedSession.messages.first.content, 'New message');
      expect(updatedSession.updatedAt, newMessage.timestamp);
    });

    test('should serialize and deserialize chat session correctly', () {
      final originalSession = ChatSession(
        id: 'session4',
        title: 'Test Serialization',
        messages: [
          Message.textMessage(id: '1', content: 'Test', sender: MessageSender.user),
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final json = originalSession.toJson();
      final deserializedSession = ChatSession.fromJson(json);

      expect(deserializedSession.id, originalSession.id);
      expect(deserializedSession.title, originalSession.title);
      expect(deserializedSession.messages.length, originalSession.messages.length);
    });
  });
}