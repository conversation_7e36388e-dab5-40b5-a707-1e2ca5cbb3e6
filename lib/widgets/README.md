# BulgeClipWidget - 枕头形状裁剪Widget

一个自定义的Flutter Widget，通过裁剪实现特殊的视觉效果：上下边缘向外凸出，左右边缘向内凹陷，形成类似枕头的形状。

## 功能特点

- ✅ 通过裁剪实现视觉效果，不改变child的原始内容
- ✅ 上下边缘向外凸出的弧形效果
- ✅ 左右边缘向内凹陷的弧形效果
- ✅ 可配置的凸出程度参数
- ✅ 平滑的抗锯齿边缘
- ✅ 预设的效果程度选项

## 使用方法

### 基本用法

```dart
import 'package:chat_flutter/widgets/bulge_clip_widget.dart';

BulgeClipWidget(
  child: Container(
    width: 200,
    height: 100,
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [Colors.blue, Colors.purple],
      ),
    ),
    child: Center(
      child: Text('Hello World'),
    ),
  ),
)
```

### 自定义凸出程度

```dart
BulgeClipWidget(
  bulgeHeight: 30.0, // 自定义凸出高度
  child: YourWidget(),
)
```

### 使用预设值

```dart
BulgeClipWidget(
  bulgeHeight: BulgePresets.dramatic, // 使用预设的夸张效果
  child: YourWidget(),
)
```

## 参数说明

### BulgeClipWidget

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `child` | `Widget` | 必需 | 要裁剪的子Widget |
| `bulgeHeight` | `double` | `20.0` | 凸出效果的高度，值越大效果越明显 |
| `clipBehavior` | `Clip` | `Clip.antiAlias` | 裁剪行为，建议使用antiAlias获得平滑边缘 |

### BulgePresets

预设的凸出效果程度：

| 预设 | 值 | 效果 |
|------|-----|------|
| `BulgePresets.subtle` | `10.0` | 微妙的凸出效果 |
| `BulgePresets.moderate` | `20.0` | 适中的凸出效果 |
| `BulgePresets.pronounced` | `30.0` | 明显的凸出效果 |
| `BulgePresets.dramatic` | `40.0` | 夸张的凸出效果 |

## 实现原理

这个Widget使用Flutter的`CustomClipper`和`ClipPath`来实现裁剪效果：

1. **BulgeClipper**: 自定义裁剪器，定义了枕头形状的裁剪路径
2. **二次贝塞尔曲线**: 使用`quadraticBezierTo`创建平滑的弧形边缘
3. **路径构建**: 
   - 上边缘：控制点在上方，创建向外凸出的弧形
   - 右边缘：控制点向左偏移，创建向内凹陷的弧形
   - 下边缘：控制点在下方，创建向外凸出的弧形
   - 左边缘：控制点向右偏移，创建向内凹陷的弧形

## 使用场景

- 特殊的卡片设计
- 图片展示效果
- 装饰性容器
- 创意UI元素
- 品牌特色设计

## 注意事项

1. **性能考虑**: 裁剪操作会有一定的性能开销，避免在滚动列表中大量使用
2. **内容适配**: 确保child的重要内容不会被裁剪掉
3. **尺寸规划**: 考虑到凸出效果，为Widget预留足够的空间
4. **测试验证**: 在不同屏幕尺寸和设备上测试效果

## 示例演示

查看 `example/lib/pages/bulge_clip_demo_page.dart` 文件获取完整的使用示例和效果演示。

## 测试

运行测试：

```bash
flutter test example/test/bulge_clip_widget_test.dart
```

测试覆盖了Widget的基本功能、参数验证和边界情况。
