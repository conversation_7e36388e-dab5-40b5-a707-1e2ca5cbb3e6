import 'package:flutter/material.dart';

/// 自定义裁剪器，实现类似枕头形状的裁剪效果
/// 上下边缘向外凸出，左右边缘向内凹陷，四个角为圆角
class BulgeClipper extends CustomClipper<Path> {
  final double bulgeHeight;
  final double? cornerRadius;

  const BulgeClipper({
    this.bulgeHeight = 20.0,
    this.cornerRadius,
  });

  @override
  Path getClip(Size size) {
    final path = Path();
    final width = size.width;
    final height = size.height;

    // 计算凸出程度和圆角半径
    final bulgeOffset = bulgeHeight;
    final radius = cornerRadius ?? (bulgeHeight * 0.6); // 圆角半径与凸出程度成比例

    // 从左上角开始，但留出圆角空间
    path.moveTo(radius, 0);

    // 上边缘：向外凸出的弧形，但两端有圆角
    path.quadraticBezierTo(
      width / 2, -bulgeOffset, // 控制点在上方
      width - radius, 0, // 结束点，留出右上角圆角空间
    );

    // 右上角圆角
    path.quadraticBezierTo(width, 0, width, radius);

    // 右边缘：向内凹陷的弧形
    path.quadraticBezierTo(
      width - bulgeOffset, height / 2, // 控制点向左偏移
      width, height - radius, // 结束点，留出右下角圆角空间
    );

    // 右下角圆角
    path.quadraticBezierTo(width, height, width - radius, height);

    // 下边缘：向外凸出的弧形，但两端有圆角
    path.quadraticBezierTo(
      width / 2, height + bulgeOffset, // 控制点在下方
      radius, height, // 结束点，留出左下角圆角空间
    );

    // 左下角圆角
    path.quadraticBezierTo(0, height, 0, height - radius);

    // 左边缘：向内凹陷的弧形
    path.quadraticBezierTo(
      bulgeOffset, height / 2, // 控制点向右偏移
      0, radius, // 结束点，留出左上角圆角空间
    );

    // 左上角圆角
    path.quadraticBezierTo(0, 0, radius, 0);

    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return oldClipper is BulgeClipper &&
           (oldClipper.bulgeHeight != bulgeHeight ||
            oldClipper.cornerRadius != cornerRadius);
  }
}

/// 自定义Widget，通过裁剪实现枕头形状的视觉效果
/// 上下边缘向外凸出，左右边缘向内凹陷，四个角为圆角
class BulgeClipWidget extends StatelessWidget {
  /// 要裁剪的子Widget
  final Widget child;

  /// 凸出效果的高度，值越大凸出效果越明显
  final double bulgeHeight;

  /// 圆角半径，如果为null则自动计算为bulgeHeight的60%
  final double? cornerRadius;

  /// 裁剪行为，默认为antiAlias以获得平滑边缘
  final Clip clipBehavior;

  const BulgeClipWidget({
    Key? key,
    required this.child,
    this.bulgeHeight = 20.0,
    this.cornerRadius,
    this.clipBehavior = Clip.antiAlias,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: BulgeClipper(
        bulgeHeight: bulgeHeight,
        cornerRadius: cornerRadius,
      ),
      clipBehavior: clipBehavior,
      child: child,
    );
  }
}

/// 预设的凸出效果程度
class BulgePresets {
  static const double subtle = 10.0;
  static const double moderate = 20.0;
  static const double pronounced = 30.0;
  static const double dramatic = 40.0;
}
