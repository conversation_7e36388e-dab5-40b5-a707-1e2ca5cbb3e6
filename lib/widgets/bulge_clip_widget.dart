import 'package:flutter/material.dart';

/// 自定义裁剪器，实现类似枕头形状的裁剪效果
/// 上下边缘向外凸出，左右边缘向内凹陷
class BulgeClipper extends CustomClipper<Path> {
  final double bulgeHeight;
  
  const BulgeClipper({
    this.bulgeHeight = 20.0,
  });

  @override
  Path getClip(Size size) {
    final path = Path();
    final width = size.width;
    final height = size.height;
    
    // 计算凸出程度，这个值决定了弧形的弯曲程度
    final bulgeOffset = bulgeHeight;
    
    // 从左上角开始绘制路径
    path.moveTo(0, bulgeOffset);
    
    // 左边缘：向内凹陷的弧形
    path.quadraticBezierTo(
      bulgeOffset, height / 2, // 控制点向右偏移
      0, height - bulgeOffset, // 结束点
    );
    
    // 下边缘：向外凸出的弧形
    path.quadraticBezierTo(
      width / 2, height + bulgeOffset, // 控制点在下方
      width, height - bulgeOffset, // 结束点
    );
    
    // 右边缘：向内凹陷的弧形
    path.quadraticBezierTo(
      width - bulgeOffset, height / 2, // 控制点向左偏移
      width, bulgeOffset, // 结束点
    );
    
    // 上边缘：向外凸出的弧形
    path.quadraticBezierTo(
      width / 2, -bulgeOffset, // 控制点在上方
      0, bulgeOffset, // 回到起始点
    );
    
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return oldClipper is BulgeClipper && oldClipper.bulgeHeight != bulgeHeight;
  }
}

/// 自定义Widget，通过裁剪实现枕头形状的视觉效果
/// 上下边缘向外凸出，左右边缘向内凹陷
class BulgeClipWidget extends StatelessWidget {
  /// 要裁剪的子Widget
  final Widget child;
  
  /// 凸出效果的高度，值越大凸出效果越明显
  final double bulgeHeight;
  
  /// 裁剪行为，默认为antiAlias以获得平滑边缘
  final Clip clipBehavior;

  const BulgeClipWidget({
    Key? key,
    required this.child,
    this.bulgeHeight = 20.0,
    this.clipBehavior = Clip.antiAlias,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: BulgeClipper(bulgeHeight: bulgeHeight),
      clipBehavior: clipBehavior,
      child: child,
    );
  }
}

/// 预设的凸出效果程度
class BulgePresets {
  static const double subtle = 10.0;
  static const double moderate = 20.0;
  static const double pronounced = 30.0;
  static const double dramatic = 40.0;
}
